#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
大麦抢票脚本快速启动器
"""
import os
import sys
import json
import argparse
from damai_ticket import DaMaiTicket


def check_config():
    """检查配置文件是否存在和有效"""
    if not os.path.exists('config.json'):
        print("❌ 配置文件 config.json 不存在")
        print("请先复制 config_example.json 为 config.json 并修改其中的参数")
        return False
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_fields = ['item_id', 'viewer', 'buy_nums', 'ticket_price']
        for field in required_fields:
            if field not in config:
                print(f"❌ 配置文件缺少必要字段: {field}")
                return False
        
        if config['item_id'] == 610820299671:
            print("⚠️  警告: 您使用的是示例商品ID，请修改为实际的商品ID")
        
        if not config['viewer'] or config['viewer'] == ["观演人姓名1"]:
            print("❌ 请在配置文件中设置正确的观演人信息")
            return False
        
        print("✅ 配置文件检查通过")
        return True
        
    except json.JSONDecodeError:
        print("❌ 配置文件格式错误，请检查JSON语法")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False


def show_config_info():
    """显示当前配置信息"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("\n📋 当前配置信息:")
        print(f"   商品ID: {config.get('item_id', 'N/A')}")
        print(f"   观演人: {config.get('viewer', 'N/A')}")
        print(f"   购买数量: {config.get('buy_nums', 'N/A')}")
        print(f"   目标票价: {config.get('ticket_price', 'N/A')}")
        print(f"   重试间隔: {config.get('retry_interval', 'N/A')} 秒")
        print(f"   最大重试: {config.get('max_retry', 'N/A')} 次")
        
    except Exception as e:
        print(f"❌ 无法显示配置信息: {e}")


def main():
    parser = argparse.ArgumentParser(description='大麦网抢票脚本快速启动器')
    parser.add_argument('--mode', type=str, default='account', 
                       choices=['account', 'qr'],
                       help='登录模式: account(账号密码) 或 qr(二维码)')
    parser.add_argument('--config', type=str, default='config.json',
                       help='配置文件路径')
    parser.add_argument('--check', action='store_true',
                       help='仅检查配置文件，不运行抢票')
    parser.add_argument('--info', action='store_true',
                       help='显示配置信息')
    
    args = parser.parse_args()
    
    print("🎫 大麦抢票脚本 V2.1 Enhanced")
    print("=" * 50)
    
    # 仅显示配置信息
    if args.info:
        show_config_info()
        return
    
    # 检查配置文件
    if not check_config():
        return
    
    # 仅检查配置
    if args.check:
        show_config_info()
        print("\n✅ 配置检查完成")
        return
    
    # 显示配置信息
    show_config_info()
    
    # 确认开始抢票
    print(f"\n🚀 准备开始抢票 (登录模式: {args.mode})")
    confirm = input("确认开始吗? (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("❌ 已取消")
        return
    
    try:
        # 读取配置
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建抢票实例
        print("\n🔧 初始化抢票程序...")
        ticket = DaMaiTicket(config)
        
        # 开始抢票
        print("🎯 开始抢票...")
        success = ticket.run(login_mode=args.mode)
        
        if success:
            print("\n🎉 抢票成功！请及时完成支付！")
        else:
            print("\n😞 抢票失败，请检查配置或稍后重试")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("请检查网络连接和配置文件")


if __name__ == '__main__':
    main()
