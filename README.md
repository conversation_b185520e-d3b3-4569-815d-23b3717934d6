# 大麦抢票脚本 V2.1 Enhanced

基于 [MakiNaruto/Automatic_ticket_purchase](https://github.com/MakiNaruto/Automatic_ticket_purchase) 项目改进的大麦网自动抢票脚本。

## ⚠️ 免责声明

本项目仅供学习交流使用，请勿用于商业用途。使用本脚本产生的任何后果由使用者自行承担。

## 功能特点

- 🚀 **高效抢票**: 通过API接口直接请求，避免页面加载延迟
- 🎯 **精准选座**: 支持自动选座购买
- 🔄 **自动重试**: 可配置重试次数和间隔
- 🍪 **Cookie管理**: 自动保存和加载登录状态
- ⚙️ **配置化**: 支持JSON配置文件，方便管理
- 🛡️ **错误处理**: 完善的异常处理和日志输出

## 安装依赖

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 安装Node.js (用于执行JavaScript签名算法)

请确保系统已安装Node.js，用于执行`signcode.js`中的签名算法。

## 配置说明

### 1. 复制配置文件

```bash
cp config_example.json config.json
```

### 2. 修改配置文件

编辑 `config.json` 文件：

```json
{
    "login_id": "your_damai_account",
    "login_password": "your_damai_password", 
    "item_id": ************,
    "viewer": ["张三"],
    "buy_nums": 1,
    "ticket_price": 180,
    "retry_interval": 0.5,
    "max_retry": 100
}
```

### 配置参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `login_id` | 大麦网登录账号 | "your_account" |
| `login_password` | 大麦网登录密码 | "your_password" |
| `item_id` | 商品ID | ************ |
| `viewer` | 观演人列表 | ["张三", "李四"] |
| `buy_nums` | 购买票数 | 2 |
| `ticket_price` | 目标票价 | 180 |
| `retry_interval` | 重试间隔(秒) | 0.5 |
| `max_retry` | 最大重试次数 | 100 |

### 3. 获取商品ID (item_id)

1. 打开大麦网商品页面
2. 从URL中获取商品ID

例如：`https://detail.damai.cn/item.htm?id=************`
商品ID为：`************`

### 4. 添加观演人

在大麦网个人中心提前添加观演人信息，确保配置文件中的观演人姓名与大麦网中的完全一致。

## 快速开始

### 1. 环境设置

```bash
# 运行环境设置脚本
python setup.py
```

### 2. 配置参数

编辑 `config.json` 文件，填入您的抢票信息。

### 3. 开始抢票

```bash
# 使用快速启动器（推荐）
python run.py

# 或直接运行主程序
python damai_ticket.py
```

## 使用方法

### 方式一：快速启动器（推荐）

```bash
# 检查配置
python run.py --check

# 显示配置信息
python run.py --info

# 账号密码登录
python run.py --mode account

# 二维码登录
python run.py --mode qr
```

### 方式二：直接运行主程序

```bash
# 账号密码登录
python damai_ticket.py --mode account

# 二维码登录
python damai_ticket.py --mode qr

# 指定配置文件
python damai_ticket.py --config my_config.json
```

### 方式三：查看使用示例

```bash
python example.py
```

## 运行流程

1. **登录验证**: 首次运行需要登录，后续会自动使用保存的cookies
2. **参数获取**: 自动获取大麦网API所需参数
3. **监控票务**: 持续监控目标商品的票务状态
4. **自动购买**: 检测到可购买状态时自动下单
5. **选座处理**: 如需选座会自动选择可用座位
6. **订单提交**: 完成购买流程并输出订单信息

## 注意事项

### 1. 环境要求

- Python 3.7+
- Chrome浏览器 (脚本会自动下载ChromeDriver)
- Node.js (用于执行JavaScript签名算法)

### 2. 使用建议

- 提前在大麦网添加观演人信息
- 确保账号有足够余额或绑定支付方式
- 建议在演出开售前几分钟启动脚本
- 网络环境要稳定，避免请求超时

### 3. 常见问题

**Q: 登录失败怎么办？**
A: 删除 `cookies.pkl` 文件重新登录，或检查账号密码是否正确。

**Q: 找不到指定价格的票？**
A: 检查 `ticket_price` 配置是否正确，确保该价位的票确实存在。

**Q: 观演人验证失败？**
A: 确保配置文件中的观演人姓名与大麦网中的完全一致。

**Q: 脚本运行但没有抢到票？**
A: 可能是网络延迟或票已售完，可以调整 `retry_interval` 参数。

## 项目结构

```
├── damai_ticket.py      # 主程序文件
├── tools.py             # 工具函数模块
├── signcode.js          # JavaScript签名算法
├── requirements.txt     # Python依赖
├── config.json          # 配置文件
├── config_example.json  # 配置文件示例
├── run.py              # 快速启动器
├── setup.py            # 环境设置工具
├── example.py          # 使用示例
├── .gitignore          # Git忽略文件
└── README.md           # 说明文档
```

## 更新日志

### V2.1 Enhanced
- 改进代码结构和错误处理
- 添加配置文件支持
- 优化登录流程
- 增强选座功能
- 添加详细的使用文档

## 致谢

本项目基于 [MakiNaruto](https://github.com/MakiNaruto) 的原始项目进行改进，感谢原作者的贡献。

## License

MIT License
