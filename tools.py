# -*- coding: UTF-8 -*-
"""
工具模块 - 包含登录、cookies管理、API参数获取等工具函数
"""
import re
import os
import json
import execjs
import pickle
import platform
import requests
from datetime import datetime
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager


def save_cookies(login_cookies):
    """保存cookies到文件"""
    try:
        with open('cookies.pkl', 'wb') as fw:
            pickle.dump(login_cookies, fw)
        print("Cookies保存成功")
    except Exception as e:
        print(f"保存cookies失败: {e}")


def load_cookies():
    """从文件读取保存的cookies"""
    try:
        with open('cookies.pkl', 'rb') as fr:
            cookies = pickle.load(fr)
        print("Cookies加载成功")
        return cookies
    except Exception as e:
        print(f"加载cookies失败: {e}")
        return {}


def check_login_status(login_cookies):
    """检测是否登录成功"""
    personal_title = '我的大麦-个人信息'
    headers = {
        'authority': 'passport.damai.cn',
        'cache-control': 'max-age=0',
        'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.83 Safari/537.36',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'referer': 'https://passport.damai.cn/login?ru=https://passport.damai.cn/accountinfo/myinfo',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    }
    
    try:
        response = requests.get('https://passport.damai.cn/accountinfo/myinfo', 
                              headers=headers, cookies=login_cookies, timeout=10)
        personal_info = BeautifulSoup(response.text, 'html.parser')
        if personal_info.title and personal_info.title.text == personal_title:
            print("登录状态验证成功")
            return True
        else:
            print("登录状态验证失败")
            return False
    except Exception as e:
        print(f"检查登录状态时出错: {e}")
        return False


def setup_chrome_driver():
    """设置Chrome浏览器驱动"""
    option = webdriver.ChromeOptions()
    # 关闭开发者模式检测
    option.add_experimental_option('excludeSwitches', ['enable-automation'])
    option.add_argument('--disable-blink-features=AutomationControlled')
    # 可选：无头模式（调试时可以注释掉）
    # option.add_argument('--headless')
    option.add_argument('--window-size=1920,1080')
    option.add_argument('--no-sandbox')
    option.add_argument('--disable-gpu')
    option.add_argument('--disable-dev-shm-usage')
    
    try:
        # 使用webdriver-manager自动管理ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=option)
        driver.set_page_load_timeout(60)
        return driver
    except Exception as e:
        print(f"设置Chrome驱动失败: {e}")
        return None


def account_login(login_type: str = 'account', login_id=None, login_password=None):
    """
    登录大麦网
    :param login_type: 登录方式 ('account' 或 'qr')
    :param login_id: 登录账号
    :param login_password: 登录密码
    :return: cookies字典
    """
    damai_title = '大麦网-全球演出赛事官方购票平台-100%正品、先付先抢、在线选座！'
    login_url = 'https://passport.damai.cn/login'
    
    driver = setup_chrome_driver()
    if not driver:
        return {}
    
    try:
        driver.get(login_url)
        
        if login_type == 'account' and login_id and login_password:
            # 账号密码登录
            print("正在进行账号密码登录...")
            # 切换到登录框架
            WebDriverWait(driver, 10).until(
                EC.frame_to_be_available_and_switch_to_it('alibaba-login-box')
            )
            
            # 输入账号密码
            username_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, 'fm-login-id'))
            )
            username_input.send_keys(login_id)
            
            password_input = driver.find_element(By.NAME, 'fm-login-password')
            password_input.send_keys(login_password)
            
            # 点击登录
            login_button = driver.find_element(By.CLASS_NAME, 'password-login')
            login_button.click()
        else:
            # 二维码或其他登录方式
            print("请在浏览器中完成登录...")
        
        # 等待登录完成
        WebDriverWait(driver, 180).until(EC.title_contains(damai_title))
        
        # 获取cookies
        login_cookies = {}
        if driver.title == damai_title:
            for cookie in driver.get_cookies():
                login_cookies[cookie['name']] = cookie['value']
            print("登录成功，获取到cookies")
        else:
            print('登录异常，请检查页面登录提示信息')
            
        return login_cookies
        
    except Exception as e:
        print(f"登录过程中出错: {e}")
        return {}
    finally:
        driver.quit()


def get_api_param():
    """
    获取请求大麦API所必须的一些参数
    可能大麦网js代码更新后需要修改此函数内的代码以重新获得参数信息
    """
    def format_param(context):
        param = []
        for line in context.split(','):
            if ':' in line:
                k, v = line.split(':', 1)
                param.append(f'"{k}":{v}')
        param_str = '{' + ','.join(param) + '}'
        try:
            param = json.loads(param_str)
            return param
        except json.JSONDecodeError:
            print("解析API参数失败")
            return {}
    
    try:
        # 获取大麦网的JS代码
        js_url = "https://g.alicdn.com/damai/??/vue-pc/0.0.70/vendor.js,vue-pc/0.0.70/perform/perform.js"
        response = requests.get(js_url, timeout=30)
        js_code_define = response.text
        
        # 获取商品SKU的API参数
        commodity_match = re.search(r'getSkuData:function.*?\|\|""}}', js_code_define)
        if commodity_match:
            commodity_param = commodity_match.group()
            data_match = re.search(r'data:\{.*?\|\|""}}', commodity_param)
            if data_match:
                commodity_param = data_match.group()
                commodity_param = commodity_param.replace('data:{', '').replace('this.vmSkuData.privilegeId||""}}', '""').replace('itemId:e', 'itemId:""')
                commodity_param = format_param(commodity_param)
            else:
                commodity_param = {}
        else:
            commodity_param = {}
        
        # 获取订单购买用户的API参数
        ex_match = re.search(r',i=Z}else\{.*?;e&&', js_code_define)
        if ex_match:
            ex_params = ex_match.group()
            ex_match2 = re.search(r'\{.*\}', ex_params)
            if ex_match2:
                ex_params = ex_match2.group()
                ex_params = ex_params.replace('{var u=', '')[1:-1]
                ex_params = format_param(ex_params)
            else:
                ex_params = {}
        else:
            ex_params = {}
        
        return commodity_param, ex_params
        
    except Exception as e:
        print(f"获取API参数失败: {e}")
        return {}, {}


def get_sign_code(h5_token: str, time_stamp, api_param) -> str:
    """
    返回请求选座信息接口必备的sign信息
    """
    try:
        node = execjs.get()
        with open('signcode.js', 'r', encoding='utf-8') as f:
            js_file = f.read()
        js_exec = node.compile(js_file)
        param1 = f'{h5_token}&{time_stamp}&12574478&'
        context = param1 + api_param
        sign_code = js_exec.call('calcaulate', context)
        return sign_code
    except Exception as e:
        print(f"生成签名失败: {e}")
        return ""


def get_select_seat_params(item_id, data_id=None):
    """获取座位信息的必备参数"""
    headers = {
        'authority': 'detail.damai.cn',
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'referer': f'https://detail.damai.cn/item.htm?id={item_id}',
        'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="100", "Google Chrome";v="100"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'script',
        'sec-fetch-mode': 'no-cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
    }

    params = {
        "itemId": item_id,
        "dataId": data_id,
        "dataType": 4,
        "apiVersion": 2.0,
        "dmChannel": "damai_pc",
        "bizCode": "ali.china.damai",
        "scenario": "itemsku"
    }

    try:
        response = requests.get('https://detail.damai.cn/subpage',
                              headers=headers, params=params, timeout=10)
        if response.status_code == 200:
            result = json.loads(response.text[5:-1])
            item_basic_info = result.get('itemBasicInfo', {})
            city_id = item_basic_info.get('nationalStandardCityId')
            project_id = item_basic_info.get('projectId')
            item_id = item_basic_info.get('itemId')
            perform_id = result.get('perform', {}).get('performId')
            return city_id, project_id, item_id, perform_id
    except Exception as e:
        print(f"获取选座参数失败: {e}")
        return None, None, None, None


def get_seat_dynamic_info(cookies, project_id, item_id, perform_id):
    """获取 standId, 用于获取所有座位信息"""
    headers = {
        'authority': 'mtop.damai.cn',
        'accept': 'application/json',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'content-type': 'application/x-www-form-urlencoded',
        'origin': 'https://seatsvc.damai.cn',
        'referer': 'https://seatsvc.damai.cn/',
        'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="100", "Google Chrome";v="100"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
    }

    try:
        h5_token = cookies.get('_m_h5_tk', '').split('_')[0]
        time_stamp = int(datetime.now().timestamp() * 1000)
        api_param = json.dumps({
            'projectId': project_id,
            'performanceId': perform_id,
            'itemId': item_id,
            'hasPromotion': 'true',
            'dmChannel': 'pc@damai_pc'
        }).replace(' ', '')

        sign_code = get_sign_code(h5_token, time_stamp, api_param)

        params = {
            'jsv': '2.6.0',
            'appKey': '12574478',
            't': time_stamp,
            'sign': sign_code,
            'type': 'originaljson',
            'dataType': 'json',
            'v': '1.0',
            'H5Request': 'true',
            'AntiCreep': 'true',
            'AntiFlood': 'true',
            'api': 'mtop.damai.wireless.seat.dynamicinfo',
            'data': api_param,
        }

        response = requests.get('https://mtop.damai.cn/h5/mtop.damai.wireless.seat.dynamicinfo/1.0/',
                              params=params, cookies=cookies, headers=headers, timeout=10)

        if response.status_code == 200:
            result = json.loads(response.text).get('data', {})
            stand_color_list = result.get('standColorList', [])
            if stand_color_list:
                stand_id = stand_color_list[0].get('standId')
                seat_price_list = result.get('priceList', [])
                return stand_id, seat_price_list
    except Exception as e:
        print(f"获取座位动态信息失败: {e}")

    return None, []


def format_valuable_seatid(all_seats_info, valuable_seats_info, price_id):
    """格式化 seatid 相关信息"""
    try:
        sid2coordinate = {}
        coordinate2sid = {}

        # 构建座位映射
        for detail in all_seats_info.get('seats', []):
            create_seat_dict(detail, coordinate2sid)
            sid2coordinate[detail.get('sid')] = {
                'sid': detail.get('sid'),
                'plid': detail.get('plid'),
                'fn': detail.get('fn'),
                'x': detail.get('x'),
                'y': detail.get('y')
            }

        # 处理不可用座位
        if 'noseat' in valuable_seats_info:
            noseat_data = valuable_seats_info.get('noseat', [])
            for line in noseat_data:
                sid = line.get('sid')
                if sid in sid2coordinate:
                    coord = sid2coordinate[sid]
                    floor, row, col = coord.get('fn'), coord.get('x'), coord.get('y')
                    if floor in coordinate2sid and row in coordinate2sid[floor] and col in coordinate2sid[floor][row]:
                        coordinate2sid[floor][row].pop(col, None)

        # 过滤指定价格的座位
        valuable_sid = {}
        if 'seat' in valuable_seats_info:
            seat_data = valuable_seats_info.get('seat', [])
            for line in seat_data:
                sid = line.get('sid')
                if sid in sid2coordinate:
                    detail = sid2coordinate[sid]
                    if detail.get('plid') == price_id:
                        create_seat_dict(detail, valuable_sid)

        return valuable_sid

    except Exception as e:
        print(f"格式化座位信息失败: {e}")
        return {}


def create_seat_dict(detail, save_dict):
    """构建座位层级信息"""
    floor = detail.get('fn')
    row = detail.get('x')
    col = detail.get('y')
    sid = detail.get('sid')

    if floor not in save_dict:
        save_dict[floor] = {}
    if row not in save_dict[floor]:
        save_dict[floor][row] = {}
    if col not in save_dict[floor][row]:
        save_dict[floor][row][col] = sid


def get_select_seat_api(cookies, perform_id, city_id):
    """得到请求所有座位信息的api地址"""
    try:
        h5_token = cookies.get('_m_h5_tk', '').split('_')[0]
        time_stamp = int(datetime.now().timestamp() * 1000)
        api_param = json.dumps({
            "cityId": city_id,
            "pfId": 2147483647 ^ int(perform_id),
            "excludestatus": True,
            "svgEncVer": "1.0",
            "dmChannel": "pc@damai_pc"
        }).replace(' ', '')

        sign_code = get_sign_code(h5_token, time_stamp, api_param)

        headers = {
            'authority': 'mtop.damai.cn',
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'content-type': 'application/x-www-form-urlencoded',
            'origin': 'https://seatsvc.damai.cn',
            'referer': 'https://seatsvc.damai.cn/',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="100", "Google Chrome";v="100"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
        }

        params = {
            'jsv': '2.6.0',
            'appKey': '12574478',
            't': time_stamp,
            'sign': sign_code,
            'type': 'originaljson',
            'dataType': 'json',
            'v': '1.3',
            'H5Request': 'true',
            'AntiCreep': 'true',
            'AntiFlood': 'true',
            'api': 'mtop.damai.wireless.project.getB2B2CAreaInfo',
            'data': api_param,
        }

        response = requests.get('https://mtop.damai.cn/h5/mtop.damai.wireless.project.getb2b2careainfo/1.3/',
                              headers=headers, params=params, cookies=cookies, timeout=10)

        if response.status_code == 200:
            result = json.loads(response.text)
            api_text = result.get('data', {}).get('result', '{}')
            api_info = json.loads(api_text).get('seatQuYu', {})
            api_address = api_info.get('resourcesPath', '')
            return api_address
    except Exception as e:
        print(f"获取选座API地址失败: {e}")
        return ""


def get_valuable_seat_id(cookies, project_id, perform_id, city_id, stand_id):
    """获取可用的座位信息"""
    try:
        h5_token = cookies.get('_m_h5_tk', '').split('_')[0]
        time_stamp = int(datetime.now().timestamp() * 1000)
        api_param = json.dumps({
            "cityId": city_id,
            "pfId": 2147483647 ^ int(perform_id),
            "standIds": stand_id,
            "channel": 100100010001,
            "projectId": project_id,
            "lessFirst": True,
            "dmChannel": "pc@damai_pc"
        }).replace(' ', '')

        sign_code = get_sign_code(h5_token, time_stamp, api_param)

        headers = {
            'authority': 'mtop.damai.cn',
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'content-type': 'application/x-www-form-urlencoded',
            'origin': 'https://seatsvc.damai.cn',
            'referer': 'https://seatsvc.damai.cn/',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="100", "Google Chrome";v="100"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
        }

        params = {
            'jsv': '2.6.0',
            'appKey': '12574478',
            't': time_stamp,
            'sign': sign_code,
            'type': 'originaljson',
            'dataType': 'json',
            'v': '1.0',
            'H5Request': 'true',
            'AntiCreep': 'true',
            'AntiFlood': 'true',
            'api': 'mtop.damai.wireless.seat.queryseatstatus',
            'data': api_param
        }

        response = requests.get('https://mtop.damai.cn/h5/mtop.damai.wireless.seat.queryseatstatus/1.0/',
                              params=params, cookies=cookies, headers=headers, timeout=10)

        if response.status_code == 200:
            seat_data = json.loads(response.text)
            return seat_data.get('data', {})
    except Exception as e:
        print(f"获取可售座位信息失败: {e}")
        return {}


def pick_seat(valuable_seat, stand_id, buy_nums):
    """简单实现选取座位信息"""
    selected_seats = []

    try:
        for floor, floor_info in valuable_seat.items():
            for row, row_info in floor_info.items():
                for col, sid in row_info.items():
                    selected_seats.append({'seatId': sid, 'standId': stand_id})
                    if len(selected_seats) == buy_nums:
                        return selected_seats
    except Exception as e:
        print(f"选择座位失败: {e}")

    return selected_seats
