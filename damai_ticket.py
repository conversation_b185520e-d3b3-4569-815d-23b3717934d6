# -*- coding: UTF-8 -*-
"""
大麦抢票脚本主程序
Author: Based on <PERSON><PERSON><PERSON><PERSON><PERSON>'s work
Version: 2.1.0 Enhanced
Description: 大麦网自动抢票脚本
"""
import re
import os
import json
import time
import argparse
import requests
from requests import session
import tools


class DaMaiTicket:
    def __init__(self, config=None):
        """
        初始化大麦抢票类
        :param config: 配置字典，包含登录信息和抢票参数
        """
        # 登录信息
        self.login_cookies = {}
        self.session = session()
        
        # 默认配置
        default_config = {
            'login_id': '',  # 大麦网登录账户名
            'login_password': '',  # 大麦网登录密码
            'item_id': 0,  # 商品id
            'viewer': [],  # 在大麦网已填写的观影人
            'buy_nums': 1,  # 购买影票数量, 需与观影人数量一致
            'ticket_price': 0,  # 购买指定票价
            'retry_interval': 0.5,  # 重试间隔（秒）
            'max_retry': 100  # 最大重试次数
        }
        
        # 合并配置
        if config:
            default_config.update(config)
        
        self.login_id = default_config['login_id']
        self.login_password = default_config['login_password']
        self.item_id = default_config['item_id']
        self.viewer = default_config['viewer']
        self.buy_nums = default_config['buy_nums']
        self.ticket_price = default_config['ticket_price']
        self.retry_interval = default_config['retry_interval']
        self.max_retry = default_config['max_retry']
        
        print(f"初始化完成 - 商品ID: {self.item_id}, 票价: {self.ticket_price}, 购买数量: {self.buy_nums}")

    def step1_get_order_info(self, item_id, commodity_param, ticket_price=None):
        """
        获取点击购买所必须的参数信息
        :param item_id: 商品id
        :param commodity_param: 获取商品购买信息必须的参数
        :param ticket_price: 购买指定价位的票
        :return: 票务信息、SKU序号、SKU ID
        """
        if not ticket_price:
            print('-' * 10, '票价未填写, 请选择票价', '-' * 10)
            return False, None, None

        commodity_param.update({'itemId': item_id})
        headers = {
            'authority': 'detail.damai.cn',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="98", "Google Chrome";v="98"',
            'sec-ch-ua-mobile': '?0',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36',
            'sec-ch-ua-platform': '"Windows"',
            'accept': '*/*',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'no-cors',
            'sec-fetch-dest': 'script',
            'referer': 'https://detail.damai.cn/item.htm',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }

        try:
            response = self.session.get('https://detail.damai.cn/subpage', 
                                      headers=headers, params=commodity_param, timeout=10)
            
            if response.status_code != 200:
                print(f"获取商品信息失败，状态码: {response.status_code}")
                return False, None, None
                
            # 解析响应
            response_text = response.text.replace('null(', '').replace('__jp0(', '')
            if response_text.endswith(')'):
                response_text = response_text[:-1]
                
            ticket_info = json.loads(response_text)
            all_ticket_sku = ticket_info.get('perform', {}).get('skuList', [])
            
            sku_id_sequence = 0
            sku_id = ''
            
            if ticket_price:
                for index, sku in enumerate(all_ticket_sku):
                    if sku.get('price') and float(sku.get('price')) == float(ticket_price):
                        sku_id_sequence = index
                        sku_id = sku.get('skuId')
                        break
                        
            if not sku_id:
                print(f"未找到价格为 {ticket_price} 的票务信息")
                return False, None, None
                
            return ticket_info, sku_id_sequence, sku_id
            
        except Exception as e:
            print(f"获取订单信息失败: {e}")
            return False, None, None

    def step2_click_buy_now(self, ex_params, sku_info):
        """
        点击立即购买
        :param ex_params: 点击立即购买按钮所发送请求的必须参数
        :param sku_info: 购买指定商品信息及数量信息
        :return: 提交订单所需信息
        """
        headers = {
            'authority': 'buy.damai.cn',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'sec-fetch-site': 'same-site',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-user': '?1',
            'sec-fetch-dest': 'document',
            'referer': 'https://detail.damai.cn/',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }

        params = {
            'exParams': json.dumps(ex_params),
            'buyParam': sku_info,
            'buyNow': 'true',
            'spm': 'a2oeg.project.projectinfo.dbuy'
        }

        try:
            response = self.session.get('https://buy.damai.cn/orderConfirm', 
                                      headers=headers, params=params, 
                                      cookies=self.login_cookies, timeout=10)
            
            if response.status_code != 200:
                print(f"点击购买失败，状态码: {response.status_code}")
                return False
                
            result = re.search(r'window\.__INIT_DATA__[\s\S]*?};', response.text)
            self.login_cookies.update(self.session.cookies)
            
            if not result:
                print("未找到订单初始化数据")
                return False
                
            try:
                submit_order_info = json.loads(result.group().replace('window.__INIT_DATA__ = ', '')[:-1])
                output_data = submit_order_info.get('output')
                if isinstance(output_data, str):
                    submit_order_info['output'] = json.loads(output_data)
                return submit_order_info
            except Exception as e:
                print(f'解析订单信息失败: {e}')
                return False
                
        except Exception as e:
            print(f"点击购买过程中出错: {e}")
            return False

    def step3_submit_order(self, submit_order_info, viewer, seat_info=None):
        """
        提交订单
        :param submit_order_info: 最终确认订单所需的所有信息
        :param viewer: 指定观演人进行购票
        :param seat_info: 座位id
        :return: 提交结果
        """
        headers = {
            'authority': 'buy.damai.cn',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="98", "Google Chrome";v="98"',
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/json;charset=UTF-8',
            'x-requested-with': 'XMLHttpRequest',
            'sec-ch-ua-mobile': '?0',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36',
            'sec-ch-ua-platform': '"Windows"',
            'origin': 'https://buy.damai.cn',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'referer': 'https://buy.damai.cn/orderConfirm?',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }

        params = (
            ('feature', '{"returnUrl":"https://orders.damai.cn/orderDetail","serviceVersion":"1.8.5"}'),
            ('submitref', 'undefined'),
        )

        try:
            # 查找观演人信息
            dm_viewer_pc = str([k for k, v in submit_order_info.get('data', {}).items()])
            dm_viewer_pc_id_search = re.search(r'dmViewerPC_[0-9]*', dm_viewer_pc)
            
            if not dm_viewer_pc_id_search:
                print("未找到观演人信息")
                return False
                
            dm_viewer_pc_id = dm_viewer_pc_id_search.group()
            user_list = submit_order_info['data'][dm_viewer_pc_id]['fields']['dmViewerList']
            all_available_user = [name.get('viewerName') for name in user_list]
            
            # 验证观演人
            if len(set(viewer).intersection(set(all_available_user))) != len(viewer):
                print('-' * 10, '请检查输入的观演人信息与大麦网观演人信息是否一致', '-' * 10)
                print(f"可用观演人: {all_available_user}")
                print(f"指定观演人: {viewer}")
                return False

            # 设置观演人
            for user in user_list:
                if user.get('viewerName') in viewer:
                    user['isUsed'] = True

            # 若为选座购买, 则需要添加座位id
            if seat_info:
                seat_ids = [seat.get('seatId') for seat in seat_info]
                seat_index = 0
                for user in user_list:
                    if seat_index >= len(viewer):
                        break
                    if user.get('viewerName') in viewer:
                        user['seatId'] = seat_ids[seat_index]
                        seat_index += 1

            submit_order_data = json.dumps(submit_order_info)
            response = self.session.post('https://buy.damai.cn/multi/trans/createOrder',
                                       headers=headers, params=params,
                                       data=submit_order_data, cookies=self.login_cookies,
                                       timeout=10)

            buy_status = json.loads(response.text)
            
            if buy_status.get('success') and buy_status.get('module', {}).get('alipayOrderId'):
                print('-' * 10, '抢票成功! 请前往大麦网确认订单', '-' * 10)
                print('订单ID:', buy_status.get('module').get('alipayOrderId'))
                print('支付链接:', buy_status.get('module').get('alipayWapCashierUrl'))
                return True
            else:
                error_msg = buy_status.get('message', '未知错误')
                print(f"提交订单失败: {error_msg}")
                return False
                
        except Exception as e:
            print(f"提交订单过程中出错: {e}")
            return False

    def run(self, login_mode='account'):
        """
        运行抢票程序
        :param login_mode: 登录模式 ('account' 或 'qr')
        """
        # 参数验证
        if len(self.viewer) != self.buy_nums:
            print('-' * 10, '购买数量与实际观演人数量不符', '-' * 10)
            return False

        if not self.item_id or not self.ticket_price:
            print('-' * 10, '请设置商品ID和票价', '-' * 10)
            return False

        # 登录处理
        if os.path.exists('cookies.pkl'):
            cookies = tools.load_cookies()
            self.login_cookies.update(cookies)
        elif login_mode == 'account' and self.login_id and self.login_password:
            self.login_cookies = tools.account_login('account', self.login_id, self.login_password)
        else:
            self.login_cookies = tools.account_login('qr')

        # 验证登录状态
        login_status = tools.check_login_status(self.login_cookies)
        if not login_status:
            print('-' * 10, '登录失败, 请检查登录账号信息', '-' * 10)
            return False
        elif login_status and not os.path.exists('cookies.pkl'):
            tools.save_cookies(self.login_cookies)

        # 获取API参数
        commodity_param, ex_params = tools.get_api_param()
        if not commodity_param or not ex_params:
            print('-' * 10, '获取API参数失败', '-' * 10)
            return False

        print(f"开始抢票 - 商品ID: {self.item_id}, 票价: {self.ticket_price}")
        print(f"观演人: {self.viewer}, 购买数量: {self.buy_nums}")

        retry_count = 0
        while retry_count < self.max_retry:
            try:
                print(f"\n第 {retry_count + 1} 次尝试...")

                # 步骤1: 获取订单信息
                ticket_info, sku_id_sequence, sku_id = self.step1_get_order_info(
                    self.item_id, commodity_param, ticket_price=self.ticket_price)

                if not ticket_info:
                    retry_count += 1
                    time.sleep(self.retry_interval)
                    continue

                # 检查票务状态
                sku_btn_list = ticket_info.get('skuPagePcBuyBtn', {}).get('skuBtnList', [])
                if sku_id_sequence >= len(sku_btn_list):
                    print("SKU序号超出范围")
                    retry_count += 1
                    time.sleep(self.retry_interval)
                    continue

                ticket_sku_status = sku_btn_list[sku_id_sequence].get('btnText', '')
                print(f"票务状态: {ticket_sku_status}")

                if ticket_sku_status == '即将开抢':
                    print("票务即将开抢，继续等待...")
                    time.sleep(self.retry_interval)
                    continue
                elif ticket_sku_status == '缺货登记':
                    print('-' * 10, f'手慢了，该票价已经售空: {ticket_sku_status}', '-' * 10)
                    return False
                elif ticket_sku_status == '立即购买':
                    # 普通购买流程
                    buy_serial_number = f'{self.item_id}_{self.buy_nums}_{sku_id}'
                    submit_order_info = self.step2_click_buy_now(ex_params, buy_serial_number)

                    if submit_order_info:
                        success = self.step3_submit_order(submit_order_info, self.viewer)
                        if success:
                            return True
                elif ticket_sku_status == '选座购买':
                    # 选座购买流程
                    print("检测到选座购买，开始选座流程...")
                    success = self.handle_seat_selection(sku_id)
                    if success:
                        return True
                else:
                    print(f"未知的票务状态: {ticket_sku_status}")

            except Exception as e:
                print(f"抢票过程中出错: {e}")

            retry_count += 1
            if retry_count < self.max_retry:
                print(f"等待 {self.retry_interval} 秒后重试...")
                time.sleep(self.retry_interval)

        print(f"抢票失败，已重试 {self.max_retry} 次")
        return False

    def handle_seat_selection(self, sku_id):
        """
        处理选座购买
        :param sku_id: SKU ID
        :return: 是否成功
        """
        try:
            # 获取选座购买必备的数据信息
            city_id, project_id, item_id, perform_id = tools.get_select_seat_params(self.item_id)
            if not all([city_id, project_id, item_id, perform_id]):
                print("获取选座参数失败")
                return False

            stand_id, seat_price_list = tools.get_seat_dynamic_info(
                self.login_cookies, project_id, item_id, perform_id)
            if not stand_id:
                print("获取座位动态信息失败")
                return False

            # 获取指定抢票价格的 sku_id, price_id
            target_sku_id, price_id = None, None
            for sku_info in seat_price_list:
                if self.ticket_price == int(sku_info.get('salePrice', 0)):
                    target_sku_id = sku_info.get('skuId')
                    price_id = sku_info.get('priceId')
                    break

            if not target_sku_id or not price_id:
                print('-' * 10, '获取sku_id失败', '-' * 10)
                return False

            # 获取座位信息API地址
            api_address = tools.get_select_seat_api(self.login_cookies, perform_id, city_id)
            if not api_address:
                print("获取座位API地址失败")
                return False

            api_address += str(stand_id) + '.json'
            response = requests.get(api_address, timeout=10)

            if response.status_code != 200:
                print(f"获取座位信息失败，状态码: {response.status_code}")
                return False

            # 获取全部的座位信息
            all_seats_info = json.loads(response.text)

            # 获取可售的座位信息
            valuable_info = tools.get_valuable_seat_id(
                self.login_cookies, project_id, perform_id, city_id, stand_id)
            if not valuable_info:
                print("获取可售座位信息失败")
                return False

            # 过滤座位信息
            valuable_seat = tools.format_valuable_seatid(all_seats_info, valuable_info, price_id)
            if not valuable_seat:
                print("没有找到可用座位")
                return False

            # 挑选座位
            seat_info = tools.pick_seat(valuable_seat, stand_id, self.buy_nums)
            if not seat_info or len(seat_info) != self.buy_nums:
                print(f"选座失败，需要 {self.buy_nums} 个座位，实际获得 {len(seat_info) if seat_info else 0} 个")
                return False

            print(f"成功选择 {len(seat_info)} 个座位")

            # 确认选座
            buy_serial_number = f'{self.item_id}_{self.buy_nums}_{sku_id}'
            submit_order_info = self.step2_click_confirm_select_seats(
                project_id, perform_id, seat_info, buy_serial_number)

            if submit_order_info:
                return self.step3_submit_order(submit_order_info, self.viewer, seat_info)
            else:
                print("确认选座失败")
                return False

        except Exception as e:
            print(f"选座购买过程中出错: {e}")
            return False

    def step2_click_confirm_select_seats(self, project_id, perform_id, seat_info, sku_info):
        """
        选座购买，点击确认选座
        """
        headers = {
            'authority': 'buy.damai.cn',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'cache-control': 'max-age=0',
            'referer': 'https://seatsvc.damai.cn/',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="100", "Google Chrome";v="100"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-site',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
        }

        params = {
            'exParams': json.dumps({
                'damai': '1',
                'channel': 'damai_app',
                'umpChannel': '10002',
                'atomSplit': '1',
                'seatInfo': seat_info,
                'serviceVersion': '2.0.0'
            }).replace(' ', ''),
            'buyParam': sku_info,
            'buyNow': 'true',
            'projectId': project_id,
            'performId': perform_id,
            'spm': 'a2oeg.selectseat.bottom.dbuy',
        }

        try:
            response = requests.get('https://buy.damai.cn/orderConfirm',
                                  params=params, cookies=self.login_cookies,
                                  headers=headers, timeout=10)

            if response.status_code == 200:
                result = re.search(r'window\.__INIT_DATA__[\s\S]*?};', response.text)
                self.login_cookies.update(self.session.cookies)

                if result:
                    try:
                        submit_order_info = json.loads(result.group().replace('window.__INIT_DATA__ = ', '')[:-1])
                        output_data = submit_order_info.get('output')
                        if isinstance(output_data, str):
                            submit_order_info['output'] = json.loads(output_data)
                        return submit_order_info
                    except Exception as e:
                        print(f'解析选座订单信息失败: {e}')
                        return False
            else:
                print(f"确认选座失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"确认选座过程中出错: {e}")
            return False


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='大麦网抢票脚本')
    parser.add_argument('--mode', type=str, default='account',
                       help='登录模式: account(账号密码登录) 或 qr(二维码登录)')
    parser.add_argument('--config', type=str, default='config.json',
                       help='配置文件路径')
    args = parser.parse_args()

    # 读取配置文件
    config = {}
    if os.path.exists(args.config):
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except Exception as e:
            print(f"读取配置文件失败: {e}")
    else:
        print(f"配置文件 {args.config} 不存在，请先创建配置文件")
        exit(1)

    # 创建抢票实例并运行
    ticket = DaMaiTicket(config)
    success = ticket.run(login_mode=args.mode)

    if success:
        print("抢票成功！")
    else:
        print("抢票失败！")
