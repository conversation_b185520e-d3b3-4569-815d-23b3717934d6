#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
大麦抢票脚本环境设置工具
"""
import os
import sys
import json
import subprocess
import platform


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    else:
        print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
        return True


def install_dependencies():
    """安装Python依赖"""
    print("\n📦 安装Python依赖包...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖包安装失败")
        print("请手动运行: pip install -r requirements.txt")
        return False


def check_nodejs():
    """检查Node.js是否安装"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js未安装或无法访问")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        print("请从 https://nodejs.org/ 下载并安装Node.js")
        return False


def create_config_file():
    """创建配置文件"""
    if os.path.exists('config.json'):
        print("⚠️  config.json已存在，跳过创建")
        return True
    
    print("\n📝 创建配置文件...")
    
    # 获取用户输入
    print("请输入以下信息 (可以稍后在config.json中修改):")
    
    login_id = input("大麦网账号 (可选): ").strip()
    login_password = input("大麦网密码 (可选): ").strip()
    
    item_id_input = input("商品ID (必填): ").strip()
    try:
        item_id = int(item_id_input) if item_id_input else 610820299671
    except ValueError:
        print("商品ID格式错误，使用默认值")
        item_id = 610820299671
    
    viewer_input = input("观演人姓名 (必填): ").strip()
    viewer = [viewer_input] if viewer_input else ["请修改为实际观演人"]
    
    buy_nums_input = input("购买数量 (默认1): ").strip()
    try:
        buy_nums = int(buy_nums_input) if buy_nums_input else 1
    except ValueError:
        buy_nums = 1
    
    ticket_price_input = input("目标票价 (必填): ").strip()
    try:
        ticket_price = int(ticket_price_input) if ticket_price_input else 180
    except ValueError:
        ticket_price = 180
    
    config = {
        "login_id": login_id,
        "login_password": login_password,
        "item_id": item_id,
        "viewer": viewer,
        "buy_nums": buy_nums,
        "ticket_price": ticket_price,
        "retry_interval": 0.5,
        "max_retry": 100
    }
    
    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        print("✅ 配置文件创建成功: config.json")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False


def show_usage_info():
    """显示使用说明"""
    print("\n📖 使用说明:")
    print("1. 基本使用:")
    print("   python run.py")
    print("   python run.py --mode qr  # 二维码登录")
    print()
    print("2. 检查配置:")
    print("   python run.py --check")
    print("   python run.py --info")
    print()
    print("3. 直接运行:")
    print("   python damai_ticket.py")
    print()
    print("4. 查看示例:")
    print("   python example.py")
    print()
    print("📋 重要提醒:")
    print("- 请在大麦网提前添加观演人信息")
    print("- 确保账号有足够余额或绑定支付方式")
    print("- 建议在开售前几分钟启动脚本")
    print("- 保持网络稳定")


def main():
    print("🎫 大麦抢票脚本环境设置")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查Node.js
    nodejs_ok = check_nodejs()
    
    # 安装依赖
    deps_ok = install_dependencies()
    
    # 创建配置文件
    config_ok = create_config_file()
    
    print("\n" + "=" * 50)
    print("📊 环境检查结果:")
    print(f"   Python版本: ✅")
    print(f"   Node.js: {'✅' if nodejs_ok else '❌'}")
    print(f"   Python依赖: {'✅' if deps_ok else '❌'}")
    print(f"   配置文件: {'✅' if config_ok else '❌'}")
    
    if all([nodejs_ok, deps_ok, config_ok]):
        print("\n🎉 环境设置完成！")
        show_usage_info()
    else:
        print("\n⚠️  环境设置未完全成功，请检查上述错误信息")
        if not nodejs_ok:
            print("   - 请安装Node.js: https://nodejs.org/")
        if not deps_ok:
            print("   - 请手动安装依赖: pip install -r requirements.txt")
        if not config_ok:
            print("   - 请手动创建配置文件")


if __name__ == '__main__':
    main()
