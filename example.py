#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
大麦抢票脚本使用示例
"""
import json
from damai_ticket import DaMaiTicket


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 配置参数
    config = {
        'login_id': 'your_account',
        'login_password': 'your_password',
        'item_id': ************,  # 商品ID
        'viewer': ['张三'],  # 观演人
        'buy_nums': 1,  # 购买数量
        'ticket_price': 180,  # 票价
        'retry_interval': 0.5,  # 重试间隔
        'max_retry': 50  # 最大重试次数
    }
    
    # 创建抢票实例
    ticket = DaMaiTicket(config)
    
    # 开始抢票
    success = ticket.run(login_mode='account')
    
    if success:
        print("抢票成功！")
    else:
        print("抢票失败！")


def example_multiple_tickets():
    """多张票购买示例"""
    print("=== 多张票购买示例 ===")
    
    config = {
        'login_id': 'your_account',
        'login_password': 'your_password',
        'item_id': ************,
        'viewer': ['张三', '李四'],  # 两个观演人
        'buy_nums': 2,  # 购买2张票
        'ticket_price': 280,
        'retry_interval': 0.3,
        'max_retry': 100
    }
    
    ticket = DaMaiTicket(config)
    success = ticket.run(login_mode='qr')  # 使用二维码登录
    
    if success:
        print("多张票抢购成功！")
    else:
        print("多张票抢购失败！")


def example_from_config_file():
    """从配置文件读取参数示例"""
    print("=== 从配置文件读取参数示例 ===")
    
    try:
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建抢票实例
        ticket = DaMaiTicket(config)
        
        # 开始抢票
        success = ticket.run()
        
        if success:
            print("抢票成功！")
        else:
            print("抢票失败！")
            
    except FileNotFoundError:
        print("配置文件 config.json 不存在")
    except json.JSONDecodeError:
        print("配置文件格式错误")
    except Exception as e:
        print(f"读取配置文件失败: {e}")


def example_custom_retry():
    """自定义重试策略示例"""
    print("=== 自定义重试策略示例 ===")
    
    config = {
        'login_id': 'your_account',
        'login_password': 'your_password',
        'item_id': ************,
        'viewer': ['张三'],
        'buy_nums': 1,
        'ticket_price': 180,
        'retry_interval': 0.1,  # 更快的重试间隔
        'max_retry': 200  # 更多的重试次数
    }
    
    ticket = DaMaiTicket(config)
    success = ticket.run()
    
    if success:
        print("抢票成功！")
    else:
        print("抢票失败！")


def create_sample_config():
    """创建示例配置文件"""
    print("=== 创建示例配置文件 ===")
    
    sample_config = {
        "login_id": "请填写您的大麦网账号",
        "login_password": "请填写您的大麦网密码",
        "item_id": ************,
        "viewer": ["请填写观演人姓名"],
        "buy_nums": 1,
        "ticket_price": 180,
        "retry_interval": 0.5,
        "max_retry": 100
    }
    
    try:
        with open('sample_config.json', 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, ensure_ascii=False, indent=4)
        print("示例配置文件已创建: sample_config.json")
        print("请修改其中的参数后使用")
    except Exception as e:
        print(f"创建配置文件失败: {e}")


if __name__ == '__main__':
    print("大麦抢票脚本使用示例")
    print("请选择要运行的示例:")
    print("1. 基本使用示例")
    print("2. 多张票购买示例")
    print("3. 从配置文件读取参数示例")
    print("4. 自定义重试策略示例")
    print("5. 创建示例配置文件")
    
    choice = input("请输入选择 (1-5): ").strip()
    
    if choice == '1':
        example_basic_usage()
    elif choice == '2':
        example_multiple_tickets()
    elif choice == '3':
        example_from_config_file()
    elif choice == '4':
        example_custom_retry()
    elif choice == '5':
        create_sample_config()
    else:
        print("无效选择")
